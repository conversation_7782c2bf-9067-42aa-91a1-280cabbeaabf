using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.ComponentModel;
using System.Windows.Data;
using Prism.Commands;
using Prism.Mvvm;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Views;

namespace DriverManagementSystem.ViewModels
{
    public class FieldVisitsLogViewModel : BindableBase
    {
        private readonly DataService _dataService;
        private FieldVisit? _selectedVisit;
        private string _searchText = string.Empty;
        private Sector? _selectedSectorFilter;
        private DateTime? _selectedDateFilter;
        private ICollectionView? _filteredVisitsView;

        public FieldVisitsLogViewModel()
        {
            try
            {
                _dataService = new DataService();

                // Initialize collections
                FieldVisits = new ObservableCollection<FieldVisit>();
                Sectors = new ObservableCollection<Sector>();

                // Initialize commands
                ViewDetailsCommand = new DelegateCommand<FieldVisit>(ViewVisitDetails);
                EditVisitCommand = new DelegateCommand<FieldVisit>(EditVisit);
                DeleteVisitCommand = new DelegateCommand<FieldVisit>(DeleteVisit);
                ClearFiltersCommand = new DelegateCommand(ClearFilters);
                RefreshDataCommand = new DelegateCommand(RefreshData);
                ForceRefreshDataCommand = new DelegateCommand(ForceRefreshData);
                CreateMessageCommand = new DelegateCommand<FieldVisit>(CreateMessage);
                ViewMessagesReportCommand = new DelegateCommand(ViewMessagesReport);
                CreateReportCommand = new DelegateCommand<FieldVisit>(CreateReport);

                // Subscribe to selection changes
                FieldVisit.SelectionChanged += OnVisitSelectionChanged;

                // الاستماع لتحديثات الزيارات من DropDataViewModel
                DropDataViewModel.FieldVisitsUpdated += OnFieldVisitsUpdated;

                // Load data
                LoadDataAsync();

                // Setup filtering
                SetupFiltering();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تهيئة سجل الزيارات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void OnVisitSelectionChanged(FieldVisit selectedVisit)
        {
            // Clear all other selections when one is selected
            foreach (var visit in FieldVisits)
            {
                if (visit != selectedVisit && visit.IsSelected)
                {
                    visit._isSelected = false; // Direct field access to avoid recursion
                    visit.NotifyPropertyChanged(nameof(visit.IsSelected));
                }
            }

            System.Diagnostics.Debug.WriteLine($"Visit selection changed: {selectedVisit.VisitNumber}");
        }

        /// <summary>
        /// معالج تحديثات الزيارات من DropDataViewModel
        /// </summary>
        private async void OnFieldVisitsUpdated()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 تم استلام إشعار تحديث الزيارات - بدء التحديث...");

                // انتظار قصير للتأكد من حفظ البيانات
                await Task.Delay(1000);

                // تحديث البيانات
                await Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    var visits = await _dataService.GetFieldVisitsAsync();
                    FieldVisits.Clear();

                    foreach (var visit in visits.OrderByDescending(v => v.AddDate))
                    {
                        FieldVisits.Add(visit);
                    }

                    UpdateStatistics();
                    FilteredVisits?.Refresh();

                    System.Diagnostics.Debug.WriteLine($"✅ تم تحديث سجل الزيارات - العدد الجديد: {FieldVisits.Count}");
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث سجل الزيارات: {ex.Message}");
            }
        }

        #region Properties

        public ObservableCollection<FieldVisit> FieldVisits { get; }
        public ObservableCollection<Sector> Sectors { get; }

        public ICollectionView FilteredVisits
        {
            get => _filteredVisitsView!;
            private set => SetProperty(ref _filteredVisitsView, value);
        }

        public FieldVisit? SelectedVisit
        {
            get => _selectedVisit;
            set => SetProperty(ref _selectedVisit, value);
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                SetProperty(ref _searchText, value);
                FilteredVisits?.Refresh();
            }
        }

        public Sector? SelectedSectorFilter
        {
            get => _selectedSectorFilter;
            set
            {
                SetProperty(ref _selectedSectorFilter, value);
                FilteredVisits?.Refresh();
            }
        }

        public DateTime? SelectedDateFilter
        {
            get => _selectedDateFilter;
            set
            {
                SetProperty(ref _selectedDateFilter, value);
                FilteredVisits?.Refresh();
            }
        }

        public int TotalVisits => FieldVisits.Count;
        public int ActiveVisits => FieldVisits.Count(v => v.IsActive);

        #endregion

        #region Commands

        public DelegateCommand<FieldVisit> ViewDetailsCommand { get; }
        public DelegateCommand<FieldVisit> EditVisitCommand { get; }
        public DelegateCommand<FieldVisit> DeleteVisitCommand { get; }
        public DelegateCommand ClearFiltersCommand { get; }
        public DelegateCommand RefreshDataCommand { get; }
        public DelegateCommand ForceRefreshDataCommand { get; }
        public DelegateCommand<FieldVisit> CreateMessageCommand { get; }
        public DelegateCommand ViewMessagesReportCommand { get; }
        public DelegateCommand<FieldVisit> CreateReportCommand { get; }

        #endregion

        #region Command Implementations

        private void ViewVisitDetails(FieldVisit visit)
        {
            if (visit == null) return;

            var details = $"📋 عقد السائق: {visit.DriverContract}\n" +
                         $"🔢 رقم الزيارة: {visit.VisitNumber}\n" +
                         $"📅 تاريخ الإضافة: {visit.AddDate:dd/MM/yyyy}\n" +
                         $"🌙 التاريخ الهجري: {visit.HijriDate}\n" +
                         $"🚀 تاريخ النزول: {visit.DepartureDate:dd/MM/yyyy}\n" +
                         $"🏠 تاريخ العودة: {visit.ReturnDate:dd/MM/yyyy}\n" +
                         $"⏱️ عدد الأيام: {visit.DaysCount}\n" +
                         $"🏢 القطاع: {visit.SectorName}\n" +
                         $"👥 عدد المشاركين: {visit.VisitorsCount}\n" +
                         $"🎯 مهمة النزول: {visit.MissionPurpose}\n\n" +
                         $"👥 المشاركون:\n";

            foreach (var visitor in visit.Visitors)
            {
                details += $"• {visitor.OfficerName} - {visitor.OfficerRank} ({visitor.OfficerCode})\n";
            }

            if (visit.Itinerary.Any())
            {
                details += "\n🗺️ خط السير:\n";
                for (int i = 0; i < visit.Itinerary.Count; i++)
                {
                    details += $"اليوم {i + 1}: {visit.Itinerary[i]}\n";
                }
            }

            if (visit.Projects.Any())
            {
                details += "\n📋 المشاريع:\n";
                foreach (var project in visit.Projects)
                {
                    details += $"• {project.ProjectNumber} - {project.ProjectName}\n";
                }
            }

            MessageBox.Show(details, $"تفاصيل الزيارة - {visit.DriverContract}", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        private void EditVisit(FieldVisit visit)
        {
            if (visit == null) return;

            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 EditVisit: بدء تعديل الزيارة {visit.VisitNumber}");

                // Try to find MainWindow through different approaches
                MainWindow? mainWindow = null;

                // Approach 1: Direct access
                if (Application.Current?.MainWindow is MainWindow mw1)
                {
                    mainWindow = mw1;
                }

                // Approach 2: Search through windows
                if (mainWindow == null)
                {
                    foreach (Window window in Application.Current.Windows)
                    {
                        if (window is MainWindow mw2)
                        {
                            mainWindow = mw2;
                            break;
                        }
                    }
                }

                if (mainWindow?.DataContext is MainViewModel mainViewModel)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ EditVisit: تم العثور على MainWindow");

                    // Navigate to DropData view
                    mainViewModel.Navigate("DropData");

                    System.Diagnostics.Debug.WriteLine($"✅ EditVisit: تم التنقل إلى صفحة جديد");

                    // Use dispatcher to ensure UI is ready
                    Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                    {
                        try
                        {
                            // Request edit through event
                            DropDataViewModel.RequestEditVisit(visit);

                            System.Diagnostics.Debug.WriteLine($"✅ EditVisit: تم إرسال طلب التعديل للزيارة {visit.VisitNumber}");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ EditVisit Dispatcher Error: {ex.Message}");
                            MessageBox.Show($"❌ خطأ في تحميل الزيارة للتعديل:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }), System.Windows.Threading.DispatcherPriority.Background);
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ EditVisit: فشل في العثور على MainWindow أو MainViewModel");
                    MessageBox.Show("❌ خطأ في الوصول للنافذة الرئيسية\nيرجى المحاولة مرة أخرى", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ EditVisit Error: {ex.Message}");
                MessageBox.Show($"❌ خطأ في تحميل الزيارة للتعديل:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeleteVisit(FieldVisit visit)
        {
            if (visit == null) return;

            var result = MessageBox.Show(
                $"⚠️ هل أنت متأكد من حذف الزيارة الميدانية وجميع البيانات المرتبطة بها؟\n\n" +
                $"📋 عقد السائق: {visit.DriverContract}\n" +
                $"🔢 رقم الزيارة: {visit.VisitNumber}\n" +
                $"🏢 القطاع: {visit.SectorName}\n\n" +
                $"🗑️ سيتم حذف جميع البيانات التالية:\n" +
                $"   • خط السير ({visit.Itinerary?.Count ?? 0} أيام)\n" +
                $"   • المشاريع ({visit.ProjectsCount} مشروع)\n" +
                $"   • القائمين بالزيارة ({visit.VisitorsCount} شخص)\n" +
                $"   • عروض السائقين المرتبطة\n" +
                $"   • توثيق الرسائل والمرفقات\n\n" +
                $"❌ هذا الإجراء لا يمكن التراجع عنه!",
                "تأكيد الحذف الشامل",
                MessageBoxButton.YesNo,
                MessageBoxImage.Warning);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    var success = await _dataService.DeleteFieldVisitAsync(visit.Id);
                    if (success)
                    {
                        FieldVisits.Remove(visit);
                        UpdateStatistics();
                        MessageBox.Show($"✅ تم حذف الزيارة {visit.DriverContract} وجميع البيانات المرتبطة بها بنجاح\n\n" +
                                      $"🔢 رقم الزيارة المحذوفة: {visit.VisitNumber}\n\n" +
                                      $"🗑️ تم حذف:\n" +
                                      $"   • خط السير\n" +
                                      $"   • المشاريع\n" +
                                      $"   • القائمين بالزيارة\n" +
                                      $"   • عروض السائقين\n" +
                                      $"   • توثيق الرسائل والمرفقات",
                                      "تم الحذف الشامل بنجاح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("❌ فشل في حذف الزيارة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"❌ خطأ في حذف الزيارة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async void ForceRefreshData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 === بدء إعادة تحميل البيانات بقوة في FieldVisitsLogViewModel ===");

                // Store current selections
                var selectedIds = FieldVisits.Where(v => v.IsSelected).Select(v => v.Id).ToList();

                // Reload data
                var visits = await _dataService.GetFieldVisitsAsync();
                FieldVisits.Clear();

                System.Diagnostics.Debug.WriteLine($"🔄 تم استلام {visits.Count} زيارة من قاعدة البيانات");

                foreach (var visit in visits.OrderByDescending(v => v.AddDate))
                {
                    // Restore selection state
                    visit.IsSelected = selectedIds.Contains(visit.Id);
                    FieldVisits.Add(visit);
                    System.Diagnostics.Debug.WriteLine($"🔄 إضافة الزيارة: {visit.VisitNumber}");
                }

                // Reload sectors
                var sectors = await _dataService.GetSectorsAsync();
                Sectors.Clear();
                Sectors.Add(new Sector { Id = 0, Name = "جميع القطاعات" }); // Add "All" option
                foreach (var sector in sectors)
                {
                    Sectors.Add(sector);
                }

                // Update statistics and refresh view
                UpdateStatistics();

                // إعادة إعداد الفلترة
                SetupFiltering();

                // تحديث العرض
                FilteredVisits?.Refresh();

                System.Diagnostics.Debug.WriteLine($"✅ تم تحديث البيانات بنجاح - العدد: {FieldVisits.Count}");

                MessageBox.Show($"✅ تم تحديث البيانات بنجاح!\n\n" +
                              $"📋 الزيارات الميدانية: {FieldVisits.Count}\n" +
                              $"🏢 القطاعات: {Sectors.Count - 1}", // -1 لاستبعاد "جميع القطاعات"
                              "تحديث البيانات", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إعادة تحميل البيانات: {ex.Message}");
                MessageBox.Show($"❌ خطأ في تحديث البيانات:\n{ex.Message}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearFilters()
        {
            SearchText = string.Empty;
            SelectedSectorFilter = null;
            SelectedDateFilter = null;
        }

        private async void RefreshData()
        {
            try
            {
                // Store current selections
                var selectedIds = FieldVisits.Where(v => v.IsSelected).Select(v => v.Id).ToList();

                // Reload data
                var visits = await _dataService.GetFieldVisitsAsync();
                FieldVisits.Clear();

                foreach (var visit in visits.OrderByDescending(v => v.AddDate))
                {
                    // Restore selection state
                    visit.IsSelected = selectedIds.Contains(visit.Id);
                    FieldVisits.Add(visit);
                }

                UpdateStatistics();
                FilteredVisits?.Refresh();

                MessageBox.Show("✅ تم تحديث البيانات بنجاح", "تحديث البيانات",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"❌ خطأ في تحديث البيانات: {ex.Message}", "خطأ",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إنشاء رسالة للزيارة المحددة - فتح النظام الاحترافي
        /// </summary>
        private void CreateMessage(FieldVisit visit)
        {
            if (visit == null) return;

            try
            {
                System.Diagnostics.Debug.WriteLine($"🚀 Creating professional message for visit: {visit.VisitNumber}");
                System.Diagnostics.Debug.WriteLine($"🔍 FieldVisitsLogViewModel - CreateMessage:");
                System.Diagnostics.Debug.WriteLine($"🔍 Visit DepartureDate: {visit.DepartureDate:dd/MM/yyyy}");
                System.Diagnostics.Debug.WriteLine($"🔍 Visit ReturnDate: {visit.ReturnDate:dd/MM/yyyy}");
                System.Diagnostics.Debug.WriteLine($"🔍 Visit DaysCount: {visit.DaysCount}");

                // إنشاء نافذة رسائل احترافية جديدة مع بيانات الزيارة
                var professionalMessagesWindow = new SFDSystem.Views.ProfessionalMessagesWindow(visit);
                professionalMessagesWindow.ShowDialog();

                System.Diagnostics.Debug.WriteLine($"✅ Professional messages window opened for visit: {visit.VisitNumber}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error creating professional message: {ex.Message}");
                MessageBox.Show($"حدث خطأ أثناء فتح نظام الرسائل الاحترافي:\n{ex.Message}",
                              "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إنشاء تقرير محضر استخراج عروض الأسعار للزيارة المحددة
        /// </summary>
        /// <param name="visit">الزيارة المحددة</param>
        private void CreateReport(FieldVisit visit)
        {
            if (visit == null) return;

            try
            {
                System.Diagnostics.Debug.WriteLine($"🚀 Creating report for visit: {visit.VisitNumber}");
                System.Diagnostics.Debug.WriteLine($"🔍 FieldVisitsLogViewModel - CreateReport:");
                System.Diagnostics.Debug.WriteLine($"🔍 Visit DepartureDate: {visit.DepartureDate:dd/MM/yyyy}");
                System.Diagnostics.Debug.WriteLine($"🔍 Visit ReturnDate: {visit.ReturnDate:dd/MM/yyyy}");
                System.Diagnostics.Debug.WriteLine($"🔍 Visit DaysCount: {visit.DaysCount}");

                // إنشاء نافذة التقرير مع بيانات الزيارة المحددة
                var reportWindow = new Views.ReportWindow(visit);
                reportWindow.ShowDialog();

                System.Diagnostics.Debug.WriteLine($"✅ Report window opened for visit: {visit.VisitNumber}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء التقرير: {ex.Message}");
                MessageBox.Show($"خطأ في إنشاء التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// عرض تقرير الرسائل المرسلة
        /// </summary>
        private void ViewMessagesReport()
        {
            try
            {
                // إحصائيات سريعة
                var totalVisits = FieldVisits.Count;
                var recentVisits = FieldVisits.Count(v => v.AddDate >= DateTime.Now.AddDays(-30));

                var report = $"📊 تقرير الرسائل\n\n" +
                           $"📋 إجمالي الزيارات: {totalVisits}\n" +
                           $"🆕 الزيارات الحديثة (30 يوم): {recentVisits}\n" +
                           $"📱 يمكن إنشاء رسالة لأي زيارة بالضغط على زر 'إنشاء رسالة'\n\n" +
                           $"💡 نصيحة: استخدم زر 'إنشاء رسالة' بجانب كل زيارة لإنشاء رسالة مخصصة للسائق";

                MessageBox.Show(report, "📊 تقرير الرسائل", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في عرض التقرير: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        #endregion

        #region Private Methods

        private async void LoadDataAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔍 === بدء تحميل البيانات في FieldVisitsLogViewModel ===");

                // Load field visits
                var visits = await _dataService.GetFieldVisitsAsync();
                FieldVisits.Clear();

                System.Diagnostics.Debug.WriteLine($"🔍 تم استلام {visits.Count} زيارة من DataService");

                // تحديث SelectedDrivers للزيارات التي لديها عروض في قاعدة البيانات
                await UpdateSelectedDriversFromDatabase(visits);

                foreach (var visit in visits.OrderByDescending(v => v.AddDate))
                {
                    System.Diagnostics.Debug.WriteLine($"🔍 إضافة الزيارة {visit.VisitNumber} - خط السير: {visit.Itinerary.Count} أيام - سائقين: '{visit.SelectedDrivers}'");
                    FieldVisits.Add(visit);
                }

                // Load sectors
                var sectors = await _dataService.GetSectorsAsync();
                Sectors.Clear();
                Sectors.Add(new Sector { Id = 0, Name = "جميع القطاعات" }); // Add "All" option
                foreach (var sector in sectors)
                {
                    Sectors.Add(sector);
                }

                UpdateStatistics();
                System.Diagnostics.Debug.WriteLine($"✅ تم تحميل البيانات بنجاح - إجمالي الزيارات: {FieldVisits.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل البيانات: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// تحديث حقل SelectedDrivers للزيارات التي لديها عروض في قاعدة البيانات
        /// </summary>
        private async Task UpdateSelectedDriversFromDatabase(System.Collections.Generic.List<FieldVisit> visits)
        {
            try
            {
                using var context = new DriverManagementSystem.Data.ApplicationDbContext();
                var offersService = new DriverManagementSystem.Services.OffersService(context);

                foreach (var visit in visits)
                {
                    // تحقق من وجود عروض لهذه الزيارة
                    var offers = await offersService.GetVisitOffersByNumberAsync(visit.VisitNumber);

                    if (offers?.Any() == true && string.IsNullOrEmpty(visit.SelectedDrivers))
                    {
                        // تحضير بيانات العروض للحفظ
                        var driversData = new System.Collections.Generic.List<string>();

                        foreach (var offer in offers)
                        {
                            var status = offer.IsWinner ? "🏆 فائز" : "تم التقديم";
                            driversData.Add($"{offer.DriverName} - {offer.FormattedAmount} - {status}");
                        }

                        // تحويل البيانات إلى نص واحد
                        var driversText = string.Join(" | ", driversData);

                        // تحديث الزيارة
                        visit.SelectedDrivers = driversText;

                        // حفظ في قاعدة البيانات
                        await _dataService.UpdateFieldVisitAsync(visit);

                        System.Diagnostics.Debug.WriteLine($"✅ تم تحديث SelectedDrivers للزيارة {visit.VisitNumber}: {driversText}");
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث SelectedDrivers: {ex.Message}");
            }
        }

        private void SetupFiltering()
        {
            FilteredVisits = CollectionViewSource.GetDefaultView(FieldVisits);
            FilteredVisits.Filter = FilterVisits;
        }

        private bool FilterVisits(object item)
        {
            if (item is not FieldVisit visit) return false;

            // Search text filter
            if (!string.IsNullOrWhiteSpace(SearchText))
            {
                var searchLower = SearchText.ToLower();
                if (!visit.DriverContract.ToLower().Contains(searchLower) &&
                    !visit.VisitNumber.ToLower().Contains(searchLower) &&
                    !visit.SectorName.ToLower().Contains(searchLower) &&
                    !visit.MissionPurpose.ToLower().Contains(searchLower))
                {
                    return false;
                }
            }

            // Sector filter
            if (SelectedSectorFilter != null && SelectedSectorFilter.Id != 0)
            {
                if (visit.SectorId != SelectedSectorFilter.Id)
                {
                    return false;
                }
            }

            // Date filter
            if (SelectedDateFilter.HasValue)
            {
                if (visit.AddDate.Date != SelectedDateFilter.Value.Date)
                {
                    return false;
                }
            }

            return true;
        }

        private void UpdateStatistics()
        {
            RaisePropertyChanged(nameof(TotalVisits));
            RaisePropertyChanged(nameof(ActiveVisits));
        }

        #endregion
    }
}
