using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;
using SFDSystem.Services;

namespace DriverManagementSystem.ViewModels
{
    /// <summary>
    /// ViewModel احترافي لإدارة الرسائل
    /// </summary>
    public class ProfessionalMessagesViewModel : INotifyPropertyChanged, IDisposable
    {
        #region Private Fields
        private readonly IDataService _dataService;
        private readonly WhatsAppService _whatsAppService;
        private string _searchText = string.Empty;
        private string _messageContent = string.Empty;
        private DriverModel _selectedDriver;
        private FieldVisit _selectedVisit;
        private bool _isSMSSelected = true;
        private bool _isWhatsAppSelected = false;
        private bool _isEmailSelected = false;
        private bool _useAutomaticSending = true;
        private bool _isIndividualSending = true;
        private bool _isGroupSending = false;
        private int _selectedDriversCount = 0;
        private bool _isDataLoaded = false; // متغير لتتبع حالة تحميل البيانات



        // Visit Info for Window Header
        private string _visitInfo = "معلومات الزيارة";

        // Visit Information Properties
        private string _visitNumber = "001";
        private string _visitConductor = "غير محدد";
        private DateTime? _expectedVisitDate = DateTime.Today.AddDays(1);
        private string _visitDuration = "3 أيام";

        // Message Templates
        private MessageTemplate _selectedMessageTemplate;
        #endregion

        #region Public Properties
        public ObservableCollection<DriverModel> AllDrivers { get; set; } = new();
        public ObservableCollection<DriverModel> FilteredDrivers { get; set; } = new();
        public ObservableCollection<DriverModel> SelectedDrivers { get; set; } = new();
        public ObservableCollection<MessageTemplate> MessageTemplates { get; set; } = new();
        public ObservableCollection<OfferResult> OffersResults { get; set; } = new();



        public MessageTemplate SelectedMessageTemplate
        {
            get => _selectedMessageTemplate;
            set
            {
                if (_selectedMessageTemplate != value)
                {
                    _selectedMessageTemplate = value;
                    OnPropertyChanged();
                    if (value != null && SelectedDriver != null)
                    {
                        GenerateMessageFromTemplate(value);
                    }
                }
            }
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                if (_searchText != value)
                {
                    _searchText = value;
                    OnPropertyChanged();
                    FilterDrivers();
                }
            }
        }

        // Last update time
        private string _lastUpdateTime = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
        public string LastUpdateTime
        {
            get => _lastUpdateTime;
            set
            {
                if (_lastUpdateTime != value)
                {
                    _lastUpdateTime = value;
                    OnPropertyChanged();
                }
            }
        }

        // Statistics properties
        public int TotalDriversCount => AllDrivers?.Count ?? 0;
        public int AvailableDriversCount => AllDrivers?.Count ?? 0; // تصحيح: استخدام AllDrivers بدلاً من FilteredDrivers
        public int SelectedDriversCount => SelectedDrivers?.Count ?? 0;

        /// <summary>
        /// نص الإحصائيات للتشخيص
        /// </summary>
        public string StatisticsDebugText => $"إجمالي: {TotalDriversCount} | متاح: {AvailableDriversCount} | محدد: {SelectedDriversCount} | مفلتر: {FilteredDrivers?.Count ?? 0}";

        /// <summary>
        /// النص المعروض للسائقين المختارين
        /// </summary>
        public string SelectedDriversDisplayText
        {
            get
            {
                var selectedDrivers = AllDrivers?.Where(d => d.IsSelected).ToList();

                if (selectedDrivers?.Any() != true)
                {
                    // إذا كانت هناك زيارة محددة، اعرض معلوماتها
                    if (SelectedVisit != null)
                    {
                        return $"🎯 الزيارة رقم: {SelectedVisit.VisitNumber} | يرجى اختيار السائقين";
                    }
                    return "👥 يرجى اختيار سائق واحد على الأقل";
                }

                if (selectedDrivers.Count == 1)
                {
                    var visitInfo = SelectedVisit != null ? $" للزيارة رقم: {SelectedVisit.VisitNumber}" : "";
                    return $"👤 السائق المختار{visitInfo}: {selectedDrivers.First().Name}";
                }

                if (selectedDrivers.Count <= 3)
                {
                    var names = string.Join(" • ", selectedDrivers.Select(d => d.Name));
                    var visitInfo = SelectedVisit != null ? $" للزيارة رقم: {SelectedVisit.VisitNumber}" : "";
                    return $"👥 السائقين المختارين{visitInfo}: {names}";
                }

                var visitInfoMultiple = SelectedVisit != null ? $" للزيارة رقم: {SelectedVisit.VisitNumber}" : "";
                return $"👥 تم اختيار {selectedDrivers.Count} سائقين{visitInfoMultiple}: {string.Join(" • ", selectedDrivers.Take(2).Select(d => d.Name))} وآخرين...";
            }
        }

        /// <summary>
        /// هل يوجد سائقين محددين
        /// </summary>
        public bool HasSelectedDrivers => SelectedDriversCount > 0;

        /// <summary>
        /// عنوان النافذة الديناميكي
        /// </summary>
        public string WindowTitle
        {
            get
            {
                if (SelectedVisit != null)
                {
                    return $"إدارة السائقين الاحترافية - الزيارة رقم: {SelectedVisit.VisitNumber}";
                }
                return "إدارة السائقين الاحترافية";
            }
        }

        /// <summary>
        /// العنوان الفرعي للنافذة
        /// </summary>
        public string WindowSubtitle
        {
            get
            {
                if (SelectedVisit != null)
                {
                    var duration = SelectedVisit.TotalProjectDays > 0 ? SelectedVisit.TotalProjectDays : SelectedVisit.DaysCount;
                    var startDate = SelectedVisit.DepartureDate.ToString("dd/MM/yyyy");
                    var endDate = SelectedVisit.ReturnDate.ToString("dd/MM/yyyy");
                    return $"مدة الزيارة: {duration} أيام | من {startDate} إلى {endDate}";
                }
                return "نظام شامل لإدارة السائقين والعقود والرحلات";
            }
        }

        // Vehicle type filter
        private List<string> _selectedVehicleTypes = new List<string>();
        public List<string> SelectedVehicleTypes
        {
            get => _selectedVehicleTypes;
            set
            {
                if (_selectedVehicleTypes != value)
                {
                    _selectedVehicleTypes = value ?? new List<string>();
                    OnPropertyChanged();
                    FilterDrivers();
                }
            }
        }

        // Vehicle capacity filter
        private List<string> _selectedVehicleCapacities = new List<string>();
        public List<string> SelectedVehicleCapacities
        {
            get => _selectedVehicleCapacities;
            set
            {
                if (_selectedVehicleCapacities != value)
                {
                    _selectedVehicleCapacities = value ?? new List<string>();
                    OnPropertyChanged();
                    FilterDrivers();
                }
            }
        }

        public string MessageContent
        {
            get => _messageContent;
            set
            {
                if (_messageContent != value)
                {
                    _messageContent = value;
                    OnPropertyChanged();
                }
            }
        }

        public DriverModel SelectedDriver
        {
            get => _selectedDriver;
            set
            {
                if (_selectedDriver != value)
                {
                    _selectedDriver = value;
                    OnPropertyChanged();
                    if (value != null)
                    {
                        GenerateMessageForDriver(value);
                    }
                }
            }
        }

        public FieldVisit SelectedVisit
        {
            get => _selectedVisit;
            set
            {
                if (_selectedVisit != value)
                {
                    _selectedVisit = value;
                    OnPropertyChanged();
                    if (value != null)
                    {
                        RefreshMessageContent();
                        // تحميل البيانات الخاصة بهذه الزيارة مرة واحدة فقط
                        _ = LoadDataForSelectedVisitOnce(value);
                    }
                }
            }
        }

        public string VisitInfo
        {
            get => _visitInfo;
            set
            {
                if (_visitInfo != value)
                {
                    _visitInfo = value;
                    OnPropertyChanged();
                }
            }
        }

        // Message Options Properties
        public bool IsSMSSelected
        {
            get => _isSMSSelected;
            set
            {
                if (_isSMSSelected != value)
                {
                    _isSMSSelected = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsWhatsAppSelected
        {
            get => _isWhatsAppSelected;
            set
            {
                if (_isWhatsAppSelected != value)
                {
                    _isWhatsAppSelected = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool IsEmailSelected
        {
            get => _isEmailSelected;
            set
            {
                if (_isEmailSelected != value)
                {
                    _isEmailSelected = value;
                    OnPropertyChanged();
                }
            }
        }

        public bool UseAutomaticSending
        {
            get => _useAutomaticSending;
            set
            {
                if (_useAutomaticSending != value)
                {
                    _useAutomaticSending = value;
                    OnPropertyChanged();

                    // تحديث إعدادات خدمة الواتس اب
                    _whatsAppService.SetSendingMode(value);

                    System.Diagnostics.Debug.WriteLine($"📋 Automatic sending set to: {value}");
                }
            }
        }

        public bool IsIndividualSending
        {
            get => _isIndividualSending;
            set
            {
                if (_isIndividualSending != value)
                {
                    _isIndividualSending = value;
                    OnPropertyChanged();

                    if (value)
                    {
                        IsGroupSending = false;
                    }

                    System.Diagnostics.Debug.WriteLine($"📋 Individual sending set to: {value}");
                }
            }
        }

        public bool IsGroupSending
        {
            get => _isGroupSending;
            set
            {
                if (_isGroupSending != value)
                {
                    _isGroupSending = value;
                    OnPropertyChanged();

                    if (value)
                    {
                        IsIndividualSending = false;
                    }

                    System.Diagnostics.Debug.WriteLine($"📋 Group sending set to: {value}");
                }
            }
        }





        // Visit Information Properties
        public string VisitNumber
        {
            get => _visitNumber;
            set
            {
                if (_visitNumber != value)
                {
                    _visitNumber = value;
                    OnPropertyChanged();
                }
            }
        }

        public string VisitConductor
        {
            get => _visitConductor;
            set
            {
                if (_visitConductor != value)
                {
                    _visitConductor = value;
                    OnPropertyChanged();
                }
            }
        }

        public DateTime? ExpectedVisitDate
        {
            get => _expectedVisitDate;
            set
            {
                if (_expectedVisitDate != value)
                {
                    _expectedVisitDate = value;
                    OnPropertyChanged();
                }
            }
        }

        public string VisitDuration
        {
            get => _visitDuration;
            set
            {
                if (_visitDuration != value)
                {
                    _visitDuration = value;
                    OnPropertyChanged();
                }
            }
        }

        /// <summary>
        /// عدد أيام الزيارة كرقم
        /// </summary>
        public int VisitDurationDays
        {
            get
            {
                try
                {
                    // إذا كانت هناك زيارة محددة، استخدم مجموع أيام المشاريع أولاً
                    if (SelectedVisit != null)
                    {
                        // إذا كان هناك مشاريع محددة، استخدم مجموع أيامها
                        if (SelectedVisit.TotalProjectDays > 0)
                        {
                            System.Diagnostics.Debug.WriteLine($"🔍 VisitDurationDays: Using TotalProjectDays = {SelectedVisit.TotalProjectDays}");
                            return SelectedVisit.TotalProjectDays;
                        }

                        // وإلا استخدم DaysCount من الزيارة
                        System.Diagnostics.Debug.WriteLine($"🔍 VisitDurationDays: Using SelectedVisit.DaysCount = {SelectedVisit.DaysCount}");
                        return SelectedVisit.DaysCount;
                    }

                    // استخراج الرقم من النص مثل "3 أيام" -> 3
                    var parts = _visitDuration.Split(' ');
                    if (parts.Length > 0 && int.TryParse(parts[0], out int days))
                    {
                        System.Diagnostics.Debug.WriteLine($"🔍 VisitDurationDays: Parsed from text = {days}");
                        return days;
                    }
                    System.Diagnostics.Debug.WriteLine($"🔍 VisitDurationDays: Using default = 3");
                    return 3; // القيمة الافتراضية
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ VisitDurationDays error: {ex.Message}");
                    return 3; // القيمة الافتراضية في حالة الخطأ
                }
            }
        }

        /// <summary>
        /// المدة الصحيحة للزيارة (للعرض في الواجهة)
        /// </summary>
        public string CorrectDuration
        {
            get
            {
                if (SelectedVisit != null)
                {
                    // استخدم مجموع أيام المشاريع إذا كان متوفراً
                    var daysToShow = SelectedVisit.TotalProjectDays > 0 ? SelectedVisit.TotalProjectDays : SelectedVisit.DaysCount;
                    return $"{daysToShow} أيام";
                }
                return VisitDuration;
            }
        }
        #endregion

        #region Helper Methods

        /// <summary>
        /// استخراج اسم السائق الحقيقي من النص (في حالة كان النص يحتوي على رسالة)
        /// </summary>
        private string ExtractDriverNameFromText(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return text;

            // إذا كان النص يحتوي على "الأخ" فهو رسالة، نستخرج الاسم منها
            if (text.Contains("الأخ"))
            {
                // البحث عن النمط: "الأخ [اسم السائق]"
                var match = System.Text.RegularExpressions.Regex.Match(text, @"الأخ\s+([^،\s]+(?:\s+[^،\s]+)*?)(?:\s*،|$)");
                if (match.Success)
                {
                    return match.Groups[1].Value.Trim();
                }
            }

            // إذا كان النص يحتوي على "السائق" فهو رسالة، نستخرج الاسم منها
            if (text.Contains("السائق"))
            {
                // البحث عن النمط: "السائق [اسم السائق]"
                var match = System.Text.RegularExpressions.Regex.Match(text, @"السائق\s+([^،\s]+(?:\s+[^،\s]+)*?)(?:\s*،|$)");
                if (match.Success)
                {
                    return match.Groups[1].Value.Trim();
                }
            }

            // إذا كان النص طويل جداً (أكثر من 50 حرف) فهو على الأرجح رسالة
            if (text.Length > 50)
            {
                // إذا لم نجد، نأخذ أول 4-5 كلمات من النص (لتغطية الأسماء الطويلة)
                var words = text.Split(' ').Where(w => !string.IsNullOrWhiteSpace(w)).Take(5).ToArray();
                if (words.Length >= 2)
                {
                    return string.Join(" ", words);
                }
            }

            // إذا لم نجد أي نمط، نعيد النص كما هو
            return text;
        }

        #endregion

        #region Commands
        public ICommand SelectAllCommand { get; }
        public ICommand ClearSelectionCommand { get; }
        public ICommand SendIndividualCommand { get; }
        public ICommand SendGroupCommand { get; }
        public ICommand SendMessageCommand { get; }
        public ICommand GenerateMessageCommand { get; }
        public ICommand SelectAllForSendingCommand { get; }
        public ICommand ClearSendingSelectionCommand { get; }
        #endregion

        #region Constructor
        public ProfessionalMessagesViewModel() : this(new DatabaseService())
        {
        }

        public ProfessionalMessagesViewModel(IDataService dataService)
        {
            System.Diagnostics.Debug.WriteLine("🔧 ProfessionalMessagesViewModel Constructor: بدء إنشاء ViewModel");

            _dataService = dataService ?? throw new ArgumentNullException(nameof(dataService));
            _whatsAppService = new WhatsAppService();

            // تعيين الإعدادات الافتراضية
            _whatsAppService.SetSendingMode(_useAutomaticSending);

            // Initialize Commands
            SelectAllCommand = new RelayCommand(SelectAllDrivers);
            ClearSelectionCommand = new RelayCommand(ClearDriverSelection);
            SendIndividualCommand = new RelayCommand(async () => await SendIndividualMessageAsync());
            SendGroupCommand = new RelayCommand(async () => await SendGroupMessageAsync());
            SendMessageCommand = new RelayCommand(async () => await SendMessageAsync());
            GenerateMessageCommand = new RelayCommand(GenerateMessageFromSelectedTemplate);
            SelectAllForSendingCommand = new RelayCommand(SelectAllForSending);
            ClearSendingSelectionCommand = new RelayCommand(ClearSendingSelection);

            // Initialize collections - تنظيف تام
            System.Diagnostics.Debug.WriteLine("🧹 تنظيف المجموعات...");
            AllDrivers = new ObservableCollection<DriverModel>();
            FilteredDrivers = new ObservableCollection<DriverModel>();
            SelectedDrivers = new ObservableCollection<DriverModel>();
            MessageTemplates = new ObservableCollection<MessageTemplate>();
            OffersResults = new ObservableCollection<OfferResult>();

            // تفعيل النماذج الاحترافية
            InitializeProfessionalTemplates();

            // Set empty message content initially
            MessageContent = "";

            System.Diagnostics.Debug.WriteLine("✅ ProfessionalMessagesViewModel Constructor: تم إنشاء ViewModel");

            // تحميل البيانات مرة واحدة فقط
            System.Diagnostics.Debug.WriteLine("🔄 Constructor: بدء تحميل البيانات الأولي...");
            _ = InitializeAsync();
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// تحميل بيانات الزيارة
        /// </summary>
        public void LoadVisitData(FieldVisit visit)
        {
            SelectedVisit = visit;

            // تحديث العناوين
            OnPropertyChanged(nameof(WindowTitle));
            OnPropertyChanged(nameof(WindowSubtitle));

            if (visit != null)
            {
                // Auto-populate visit information
                VisitNumber = visit.VisitNumber ?? "001";

                // Get first visitor name as conductor
                if (visit.Visitors != null && visit.Visitors.Any())
                {
                    var firstVisitor = visit.Visitors.FirstOrDefault(v => !string.IsNullOrWhiteSpace(v.OfficerName));
                    VisitConductor = firstVisitor?.OfficerName ?? "غير محدد";
                }
                else
                {
                    VisitConductor = "غير محدد";
                }

                // Set expected visit date
                ExpectedVisitDate = visit.DepartureDate;

                // Calculate duration
                var duration = (visit.ReturnDate - visit.DepartureDate).Days + 1;
                VisitDuration = $"{duration} أيام";

                // تحديث CorrectDuration
                OnPropertyChanged(nameof(CorrectDuration));

                System.Diagnostics.Debug.WriteLine($"🔍 ProfessionalMessagesViewModel - LoadVisitData:");
                System.Diagnostics.Debug.WriteLine($"🔍 DepartureDate: {visit.DepartureDate:dd/MM/yyyy}");
                System.Diagnostics.Debug.WriteLine($"🔍 ReturnDate: {visit.ReturnDate:dd/MM/yyyy}");
                System.Diagnostics.Debug.WriteLine($"🔍 Calculated duration: {duration} days");
                System.Diagnostics.Debug.WriteLine($"🔍 VisitDuration string: '{VisitDuration}'");
                System.Diagnostics.Debug.WriteLine($"🔍 VisitDurationDays property: {VisitDurationDays}");
                System.Diagnostics.Debug.WriteLine($"🔍 CorrectDuration property: '{CorrectDuration}'");
            }

            GenerateDefaultMessage();
        }



        /// <summary>
        /// الحصول على السائقين المحددين
        /// </summary>
        /// <returns>مجموعة السائقين المحددين</returns>
        public ObservableCollection<Driver> GetSelectedDrivers()
        {
            try
            {
                var selectedDrivers = new ObservableCollection<Driver>();

                foreach (var driverModel in AllDrivers.Where(d => d.IsSelected))
                {
                    var driver = new Driver
                    {
                        Id = 0, // سيتم تعيين ID من قاعدة البيانات
                        Name = driverModel.Name,
                        DriverCode = driverModel.DriverCode,
                        PhoneNumber = driverModel.PhoneNumber,
                        VehicleType = driverModel.VehicleType,
                        VehicleNumber = "", // سيتم تعيينه لاحقاً
                        IsActive = driverModel.Status == "متاح",
                        IsSelected = driverModel.IsSelected
                    };

                    selectedDrivers.Add(driver);
                }

                System.Diagnostics.Debug.WriteLine($"✅ Retrieved {selectedDrivers.Count} selected drivers");
                return selectedDrivers;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error getting selected drivers: {ex.Message}");
                return new ObservableCollection<Driver>();
            }
        }

        /// <summary>
        /// توليد نص الرسالة للسائق
        /// </summary>
        /// <param name="driverName">اسم السائق</param>
        /// <returns>نص الرسالة</returns>
        public string GenerateDriverMessage(string driverName)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔍 INDIVIDUAL MESSAGE - SelectedVisit: {SelectedVisit?.VisitNumber ?? "NULL"}");
                System.Diagnostics.Debug.WriteLine($"🔍 INDIVIDUAL MESSAGE - Driver: {driverName}");

                if (SelectedVisit == null)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ INDIVIDUAL MESSAGE - SelectedVisit is NULL");
                    return $"الأخ/{driverName} المحترم، تم اختياركم للزيارة الميدانية";
                }

                // بناء نص خط السير مع تفاصيل الأيام
                var itineraryText = "المناطق المحددة";

                // استخدام تفاصيل الأيام من Itinerary مع أسماء الأيام العربية (نفس منطق GenerateGroupMessage)
                System.Diagnostics.Debug.WriteLine($"🔍 INDIVIDUAL MESSAGE - SelectedVisit.Itinerary count: {SelectedVisit.Itinerary?.Count ?? 0}");
                if (SelectedVisit.Itinerary?.Any() == true)
                {
                    // استخدام نفس منطق GenerateGroupMessage مع أسماء الأيام العربية
                    var dayTexts = new List<string>();
                    for (int i = 0; i < SelectedVisit.Itinerary.Count; i++)
                    {
                        System.Diagnostics.Debug.WriteLine($"🔍 INDIVIDUAL MESSAGE - Processing day {i + 1}: '{SelectedVisit.Itinerary[i]}'");
                        if (!string.IsNullOrWhiteSpace(SelectedVisit.Itinerary[i]))
                        {
                            var dayName = GetArabicDayName(i + 1);
                            var formattedDay = $"- اليوم {dayName}: {SelectedVisit.Itinerary[i]}";
                            dayTexts.Add(formattedDay);
                            System.Diagnostics.Debug.WriteLine($"✅ INDIVIDUAL MESSAGE - Added formatted day: '{formattedDay}'");
                        }
                    }

                    if (dayTexts.Any())
                    {
                        itineraryText = string.Join("\n", dayTexts);
                        System.Diagnostics.Debug.WriteLine($"✅ INDIVIDUAL MESSAGE - Final itinerary text with Arabic day names: '{itineraryText}'");
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"❌ INDIVIDUAL MESSAGE - No itinerary data found");
                }

                // بناء نص القائمين بالزيارة (الأسماء فقط بدون الرتب)
                var visitorsText = "الفريق المختص";
                if (SelectedVisit.Visitors?.Any() == true)
                {
                    visitorsText = string.Join(" و ", SelectedVisit.Visitors.Select(v => v.OfficerName));
                }

                // حساب تواريخ النزول والعودة
                var startDate = SelectedVisit.DepartureDate.ToString("dd/MM/yyyy");
                var endDate = SelectedVisit.ReturnDate.ToString("dd/MM/yyyy");

                // حساب عدد الأيام بطريقة ذكية
                var daysToShow = SelectedVisit.TotalProjectDays > 0 ? SelectedVisit.TotalProjectDays : SelectedVisit.DaysCount;

                // إذا كانت الأيام صفر، احسب من التواريخ
                if (daysToShow <= 0)
                {
                    var daysDifference = (SelectedVisit.ReturnDate - SelectedVisit.DepartureDate).Days + 1;
                    daysToShow = daysDifference > 0 ? daysDifference : 3; // افتراضي 3 أيام
                }

                var messageText = $@"الأخ/{driverName}،
يرجى تقديم عرض سعركم خلال 24 ساعة وذلك للسفر لمدة ({daysToShow} أيام) مع الأخ:ت/ {visitorsText}
في المناطق التالية:
{itineraryText} 📅 تاريخ النزول: {startDate} ...وشكراً ";

                return messageText;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في توليد نص الرسالة: {ex.Message}");
                return $"الأخ/{driverName} المحترم، تم اختياركم للزيارة الميدانية رقم {SelectedVisit?.VisitNumber ?? "غير محدد"}";
            }
        }

        /// <summary>
        /// إضافة نتيجة عرض سعر جديدة
        /// </summary>
        /// <param name="driverName">اسم السائق</param>
        /// <param name="offeredPrice">السعر المقدم</param>
        /// <param name="status">الحالة</param>
        /// <param name="messageText">نص الرسالة</param>
        public void AddOfferResult(string driverName, string offeredPrice, string status = "تم التقديم", string messageText = "")
        {
            try
            {
                // التأكد من أن اسم السائق صحيح وليس نص رسالة
                var cleanDriverName = ExtractDriverNameFromText(driverName);

                var existingOffer = OffersResults.FirstOrDefault(o => o.DriverName == cleanDriverName);
                if (existingOffer != null)
                {
                    // تحديث العرض الموجود
                    existingOffer.OfferedPrice = offeredPrice;
                    existingOffer.Status = status;
                    existingOffer.SubmissionDate = DateTime.Now.ToString("yyyy/MM/dd HH:mm");
                    if (!string.IsNullOrEmpty(messageText))
                    {
                        existingOffer.MessageText = messageText;
                    }
                }
                else
                {
                    // إضافة عرض جديد
                    OffersResults.Add(new OfferResult
                    {
                        DriverName = cleanDriverName,
                        OfferedPrice = offeredPrice,
                        Status = status,
                        SubmissionDate = DateTime.Now.ToString("yyyy/MM/dd HH:mm"),
                        MessageText = messageText
                    });
                }

                // إعادة ترتيب العروض حسب السعر (من الأقل إلى الأعلى)
                SortOffersByPrice();

                System.Diagnostics.Debug.WriteLine($"✅ Added offer result for driver: {driverName} - {offeredPrice}. Total offers: {OffersResults.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error adding offer result: {ex.Message}");
            }
        }

        /// <summary>
        /// ترتيب العروض حسب السعر (من الأقل إلى الأعلى)
        /// </summary>
        private void SortOffersByPrice()
        {
            try
            {
                if (OffersResults.Count <= 1) return;

                // تحويل العروض إلى قائمة وترتيبها
                var sortedOffers = OffersResults
                    .OrderBy(offer =>
                    {
                        // استخراج الرقم من النص (إزالة "ريال" والفواصل)
                        var priceText = offer.OfferedPrice?.Replace("ريال", "").Replace(",", "").Trim() ?? "0";
                        return decimal.TryParse(priceText, out decimal price) ? price : decimal.MaxValue;
                    })
                    .ToList();

                // مسح القائمة الحالية وإعادة إضافة العناصر مرتبة
                OffersResults.Clear();
                foreach (var offer in sortedOffers)
                {
                    OffersResults.Add(offer);
                }

                System.Diagnostics.Debug.WriteLine($"🎯 Sorted {sortedOffers.Count} offers by price (lowest first)");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error sorting offers by price: {ex.Message}");
            }
        }

        /// <summary>
        /// حفظ السائقين المحددين في سجل الزيارات الميدانية
        /// </summary>
        public async Task<bool> SaveSelectedDriversToFieldVisit()
        {
            try
            {
                // التحقق من وجود عروض للحفظ
                if (OffersResults?.Any() != true)
                {
                    System.Diagnostics.Debug.WriteLine("❌ No offers to save to field visit");
                    return false;
                }

                // تحضير بيانات العروض للحفظ
                var driversData = new List<string>();

                foreach (var offer in OffersResults)
                {
                    driversData.Add($"{offer.DriverName} - {offer.OfferedPrice} - {offer.Status}");
                }

                // تحويل البيانات إلى نص واحد
                var driversText = string.Join(" | ", driversData);

                // حفظ في قاعدة البيانات
                var success = await SaveDriversDataToDatabase(driversText);

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ Saved {OffersResults.Count} offers to field visit log");
                    System.Diagnostics.Debug.WriteLine($"📝 Data: {driversText}");
                }

                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error saving offers to field visit: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// حفظ بيانات السائقين في قاعدة البيانات مرتبطة برقم الزيارة المحدد
        /// </summary>
        private async Task<bool> SaveDriversDataToDatabase(string driversData)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 Starting SaveDriversDataToDatabase with data: {driversData}");

                // التحقق من وجود زيارة محددة حالياً
                if (SelectedVisit != null)
                {
                    System.Diagnostics.Debug.WriteLine($"🔍 Updating selected visit: {SelectedVisit.VisitNumber} (ID: {SelectedVisit.Id})");

                    // حفظ البيانات في الزيارة المحددة
                    var oldData = SelectedVisit.SelectedDrivers;
                    SelectedVisit.SelectedDrivers = driversData;

                    System.Diagnostics.Debug.WriteLine($"📝 Old data: '{oldData}'");
                    System.Diagnostics.Debug.WriteLine($"📝 New data: '{driversData}'");

                    var success = await _dataService.UpdateFieldVisitAsync(SelectedVisit);

                    if (success)
                    {
                        System.Diagnostics.Debug.WriteLine($"✅ Successfully updated field visit {SelectedVisit.VisitNumber} with drivers data");
                        return true;
                    }
                    else
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ Failed to update field visit {SelectedVisit.VisitNumber}");
                        return false;
                    }
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("🔍 No selected visit, searching for latest visit");

                    // البحث عن آخر زيارة إذا لم تكن هناك زيارة محددة
                    var fieldVisits = await _dataService.GetFieldVisitsAsync();
                    var latestVisit = fieldVisits.OrderByDescending(fv => fv.CreatedAt).FirstOrDefault();

                    if (latestVisit != null)
                    {
                        System.Diagnostics.Debug.WriteLine($"🔍 Found latest visit: {latestVisit.VisitNumber} (ID: {latestVisit.Id})");

                        // تحديث آخر زيارة
                        latestVisit.SelectedDrivers = driversData;
                        var success = await _dataService.UpdateFieldVisitAsync(latestVisit);

                        if (success)
                        {
                            System.Diagnostics.Debug.WriteLine($"✅ Successfully updated latest field visit {latestVisit.VisitNumber} with drivers data");
                            return true;
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ Failed to update latest field visit {latestVisit.VisitNumber}");
                            return false;
                        }
                    }
                    else
                    {
                        // حفظ مؤقت في الإعدادات
                        System.Diagnostics.Debug.WriteLine("⚠️ No field visits found. Saving to settings temporarily.");
                        Properties.Settings.Default.SavedDriversData = driversData;
                        Properties.Settings.Default.Save();
                        System.Diagnostics.Debug.WriteLine($"✅ Saved drivers data to application settings temporarily");
                        return true;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Database save error: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"❌ Stack trace: {ex.StackTrace}");
                return false;
            }
        }

        /// <summary>
        /// تحميل البيانات الخاصة بالزيارة المحددة مرة واحدة فقط
        /// </summary>
        private async Task LoadDataForSelectedVisitOnce(FieldVisit visit)
        {
            try
            {
                if (visit == null) return;

                System.Diagnostics.Debug.WriteLine($"🔄 LoadDataForSelectedVisitOnce: بدء تحميل البيانات للزيارة {visit.VisitNumber}");

                // مسح البيانات الحالية تماماً
                ClearAllDataSilently();

                // تحميل العروض من قاعدة البيانات مرة واحدة فقط
                await LoadOffersFromDatabaseOnce(visit.VisitNumber);

                System.Diagnostics.Debug.WriteLine($"✅ LoadDataForSelectedVisitOnce: تم تحميل البيانات للزيارة {visit.VisitNumber}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في LoadDataForSelectedVisitOnce: {ex.Message}");
            }
        }

        /// <summary>
        /// مسح جميع البيانات بصمت دون حفظ
        /// </summary>
        private void ClearAllDataSilently()
        {
            SelectedDrivers.Clear();
            OffersResults.Clear();

            // إعادة تعيين حالة التحديد لجميع السائقين
            foreach (var driver in AllDrivers)
            {
                driver.IsSelected = false;
            }

            System.Diagnostics.Debug.WriteLine("🧹 تم مسح جميع البيانات بصمت");
        }

        /// <summary>
        /// تحميل العروض من قاعدة البيانات مرة واحدة فقط
        /// </summary>
        private async Task LoadOffersFromDatabaseOnce(string visitNumber)
        {
            try
            {
                using var context = new DriverManagementSystem.Data.ApplicationDbContext();
                var offersService = new DriverManagementSystem.Services.OffersService(context);
                var savedOffers = await offersService.GetVisitOffersByNumberAsync(visitNumber);

                if (savedOffers?.Any() == true)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ تم العثور على {savedOffers.Count} عرض في قاعدة البيانات للزيارة {visitNumber}");

                    foreach (var offer in savedOffers.OrderBy(o => o.ProposedAmount))
                    {
                        // البحث عن السائق في القائمة الأساسية
                        var driver = AllDrivers.FirstOrDefault(d => d.Name == offer.DriverName);
                        if (driver != null)
                        {
                            // إضافة السائق للمحددين
                            driver.IsSelected = true;
                            if (!SelectedDrivers.Contains(driver))
                            {
                                SelectedDrivers.Add(driver);
                            }
                        }

                        // إضافة العرض للنتائج مع دعم جميع الحالات
                        var status = this.GetOfferStatusText(offer);
                        var formattedPrice = $"{offer.ProposedAmount:N0} ريال";
                        var messageText = GenerateDriverMessage(offer.DriverName);

                        // التأكد من عدم وجود العرض مسبقاً
                        var existingOffer = OffersResults.FirstOrDefault(o => o.DriverName == offer.DriverName);
                        if (existingOffer == null)
                        {
                            OffersResults.Add(new OfferResult
                            {
                                DriverName = offer.DriverName,
                                OfferedPrice = formattedPrice,
                                Status = status,
                                SubmissionDate = offer.CreatedAt.ToString("yyyy/MM/dd"),
                                MessageText = messageText
                            });

                            System.Diagnostics.Debug.WriteLine($"✅ تم إضافة عرض: {offer.DriverName} - {formattedPrice} - {status}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ العرض موجود مسبقاً: {offer.DriverName}");
                        }
                    }

                    // ترتيب العروض حسب السعر
                    SortOffersByPrice();
                    UpdateSelectedCount();

                    System.Diagnostics.Debug.WriteLine($"🎯 تم تحميل {OffersResults.Count} عرض نهائي للزيارة {visitNumber}");
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine($"ℹ️ لا توجد عروض محفوظة للزيارة {visitNumber}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل العروض من قاعدة البيانات: {ex.Message}");
            }
        }

        /// <summary>
        /// تحميل البيانات الخاصة بالزيارة المحددة - الدالة القديمة (محفوظة للتوافق)
        /// </summary>
        private async Task LoadDataForSelectedVisit(FieldVisit visit)
        {
            // استدعاء الدالة الجديدة
            await LoadDataForSelectedVisitOnce(visit);
        }

        /// <summary>
        /// تحميل بيانات السائقين المحفوظة من آخر زيارة ميدانية أو الإعدادات
        /// </summary>
        public async Task LoadSavedDriversData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 LoadSavedDriversData: بدء تحميل البيانات المحفوظة...");

                // إذا كانت هناك زيارة محددة، تحميل بياناتها
                if (SelectedVisit != null)
                {
                    System.Diagnostics.Debug.WriteLine($"📋 تحميل بيانات الزيارة المحددة: {SelectedVisit.VisitNumber}");
                    await LoadDataForSelectedVisit(SelectedVisit);
                    return;
                }

                string savedData = null;
                string source = "";

                // أولاً: محاولة التحميل من الزيارات الميدانية
                var fieldVisits = await _dataService.GetFieldVisitsAsync();
                var latestVisit = fieldVisits
                    .Where(fv => !string.IsNullOrEmpty(fv.SelectedDrivers))
                    .OrderByDescending(fv => fv.CreatedAt)
                    .FirstOrDefault();

                if (latestVisit != null && !string.IsNullOrEmpty(latestVisit.SelectedDrivers))
                {
                    savedData = latestVisit.SelectedDrivers;
                    source = $"field visit {latestVisit.VisitNumber}";
                }
                else
                {
                    // ثانياً: محاولة التحميل من الإعدادات
                    var settingsData = Properties.Settings.Default.SavedDriversData;
                    if (!string.IsNullOrEmpty(settingsData))
                    {
                        savedData = settingsData;
                        source = "application settings";
                    }
                }

                if (!string.IsNullOrEmpty(savedData))
                {
                    // تحليل بيانات السائقين المحفوظة
                    var driversData = savedData.Split(" | ");

                    // مسح البيانات الحالية
                    SelectedDrivers.Clear();
                    OffersResults.Clear();

                    foreach (var driverData in driversData)
                    {
                        var parts = driverData.Split(" - ");
                        if (parts.Length >= 2)
                        {
                            var driverName = parts[0];
                            var price = parts[1];
                            var status = parts.Length > 2 ? parts[2] : "تم التقديم";

                            // البحث عن السائق في القائمة الأساسية
                            var driver = AllDrivers.FirstOrDefault(d => d.Name == driverName);
                            if (driver != null)
                            {
                                // إضافة السائق للمحددين
                                driver.IsSelected = true;
                                SelectedDrivers.Add(driver);

                                // إضافة العرض للنتائج
                                OffersResults.Add(new OfferResult
                                {
                                    DriverName = driverName,
                                    OfferedPrice = price,
                                    Status = status,
                                    SubmissionDate = DateTime.Now.ToString("yyyy/MM/dd"),
                                    MessageText = GenerateDriverMessage(driverName)
                                });
                            }
                        }
                    }

                    // ترتيب العروض حسب السعر بعد التحميل
                    SortOffersByPrice();

                    System.Diagnostics.Debug.WriteLine($"🔄 Loaded {SelectedDrivers.Count} saved drivers from {source}");
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error loading saved drivers data: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على قائمة الزيارات الميدانية
        /// </summary>
        public async Task<List<FieldVisit>> GetFieldVisitsAsync()
        {
            try
            {
                return await _dataService.GetFieldVisitsAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error getting field visits: {ex.Message}");
                return new List<FieldVisit>();
            }
        }

        /// <summary>
        /// إعادة ترتيب العروض حسب السعر (دالة عامة)
        /// </summary>
        public void RefreshOffersOrder()
        {
            try
            {
                SortOffersByPrice();
                System.Diagnostics.Debug.WriteLine("🔄 Refreshed offers order by price");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error refreshing offers order: {ex.Message}");
            }
        }

        /// <summary>
        /// حفظ العروض في قاعدة البيانات
        /// </summary>
        public async Task<bool> SaveOffersToDatabase()
        {
            try
            {
                if (OffersResults?.Any() != true || SelectedVisit == null)
                {
                    System.Diagnostics.Debug.WriteLine("❌ No offers to save or no visit selected");
                    return false;
                }

                using var context = new DriverManagementSystem.Data.ApplicationDbContext();
                var offersService = new DriverManagementSystem.Services.OffersService(context);

                // مسح العروض القديمة للزيارة
                await offersService.DeleteVisitOffersAsync(SelectedVisit.VisitNumber);

                // إنشاء قائمة العروض الجديدة
                var offersToSave = new List<DriverManagementSystem.Models.DriverOffer>();

                foreach (var offerResult in OffersResults)
                {
                    // البحث عن السائق في القائمة الأساسية
                    var driver = AllDrivers?.FirstOrDefault(d => d.Name == offerResult.DriverName);

                    // استخراج المبلغ من النص
                    var amountText = offerResult.OfferedPrice?.Replace("ريال", "").Replace(",", "").Trim() ?? "0";
                    decimal.TryParse(amountText, out decimal amount);

                    // تحديد حالة الفوز
                    bool isWinner = offerResult.Status?.Contains("فائز") == true || offerResult.Status?.Contains("🏆") == true;

                    var driverOffer = new DriverManagementSystem.Models.DriverOffer
                    {
                        VisitNumber = SelectedVisit.VisitNumber,
                        DriverName = offerResult.DriverName ?? "",
                        DriverCode = driver?.DriverCode ?? "",
                        PhoneNumber = driver?.PhoneNumber ?? "",
                        VehicleType = driver?.VehicleType ?? "",
                        VehicleNumber = "غير محدد",
                        DaysCount = SelectedVisit.DaysCount,
                        ProposedAmount = amount,
                        IsSelected = true,
                        IsWinner = isWinner,
                        OfferStatus = offerResult.Status ?? "تم التقديم",
                        CreatedAt = DateTime.Now
                    };

                    offersToSave.Add(driverOffer);
                }

                // حفظ العروض في قاعدة البيانات
                var success = await offersService.SaveOffersAsync(offersToSave);

                if (success)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ Successfully saved {offersToSave.Count} offers to database for visit {SelectedVisit.VisitNumber}");

                    // حفظ معرف الزيارة في الإعدادات للاستخدام لاحقاً
                    Properties.Settings.Default.LastSavedVisitNumber = SelectedVisit.VisitNumber;
                    Properties.Settings.Default.Save();
                }
                else
                {
                    System.Diagnostics.Debug.WriteLine("❌ Failed to save offers to database");
                }

                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error saving offers to database: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// تحميل العروض المحفوظة من قاعدة البيانات
        /// </summary>
        public async Task LoadSavedOffersFromDatabase()
        {
            try
            {
                if (SelectedVisit == null)
                {
                    System.Diagnostics.Debug.WriteLine("❌ No visit selected for loading offers");
                    return;
                }

                System.Diagnostics.Debug.WriteLine($"⚠️ LoadSavedOffersFromDatabase تم استدعاؤها للزيارة {SelectedVisit.VisitNumber} - سيتم تجاهلها لتجنب التكرار");

                // لا نفعل شيئاً هنا لتجنب التكرار
                // البيانات يتم تحميلها في LoadDataForSelectedVisitOnce
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error loading saved offers from database: {ex.Message}");
            }
        }

        /// <summary>
        /// مسح جميع نتائج العروض للزيارة المحددة
        /// </summary>
        public async void ClearOffersResults()
        {
            try
            {
                OffersResults.Clear();
                SelectedDrivers.Clear();

                // إعادة تعيين حالة التحديد لجميع السائقين
                foreach (var driver in AllDrivers)
                {
                    driver.IsSelected = false;
                }

                // مسح البيانات من الزيارة المحددة
                if (SelectedVisit != null)
                {
                    SelectedVisit.SelectedDrivers = "";
                    await _dataService.UpdateFieldVisitAsync(SelectedVisit);
                    System.Diagnostics.Debug.WriteLine($"🧹 Cleared offers results for visit {SelectedVisit.VisitNumber}");
                }
                else
                {
                    // مسح البيانات المحفوظة في الإعدادات إذا لم تكن هناك زيارة محددة
                    Properties.Settings.Default.SavedDriversData = "";
                    Properties.Settings.Default.Save();
                    System.Diagnostics.Debug.WriteLine("🧹 Cleared offers results from settings");
                }

                UpdateSelectedCount();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error clearing offers results: {ex.Message}");
            }
        }

        /// <summary>
        /// تنظيف الموارد
        /// </summary>
        public void Dispose()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🧹 ProfessionalMessagesViewModel Dispose: بدء تنظيف الموارد");

                // تنظيف المجموعات
                AllDrivers?.Clear();
                FilteredDrivers?.Clear();
                SelectedDrivers?.Clear();
                OffersResults?.Clear();
                MessageTemplates?.Clear();

                // تنظيف الخدمات
                _whatsAppService?.Dispose();

                // تنظيف المتغيرات
                _selectedVisit = null;
                _selectedDriver = null;
                _selectedMessageTemplate = null;

                System.Diagnostics.Debug.WriteLine("✅ ProfessionalMessagesViewModel resources cleaned up completely");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ Error disposing ViewModel: {ex.Message}");
            }
        }
        #endregion

        #region Private Methods

        /// <summary>
        /// تنظيف شامل لجميع البيانات وإزالة Event Handlers
        /// </summary>
        private void CleanupAllData()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🧹 بدء التنظيف الشامل...");

                // إزالة Event Handlers من جميع السائقين
                foreach (var driver in AllDrivers)
                {
                    // إزالة جميع Event Handlers
                    var propertyChangedField = typeof(DriverModel).GetField("PropertyChanged",
                        System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);

                    if (propertyChangedField != null)
                    {
                        propertyChangedField.SetValue(driver, null);
                    }
                }

                // مسح جميع المجموعات
                AllDrivers.Clear();
                FilteredDrivers.Clear();
                SelectedDrivers.Clear();

                // إعادة تعيين المتغيرات
                _selectedDriversCount = 0;
                _searchText = string.Empty;
                _selectedVehicleTypes = new List<string>();
                _isDataLoaded = false; // إعادة تعيين حالة تحميل البيانات

                System.Diagnostics.Debug.WriteLine("✅ تم التنظيف الشامل بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التنظيف الشامل: {ex.Message}");
            }
        }

        /// <summary>
        /// تفعيل النماذج الاحترافية للرسائل
        /// </summary>
        private void InitializeProfessionalTemplates()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🎨 تفعيل النماذج الاحترافية للرسائل...");

                // تفعيل قوالب الرسائل الاحترافية
                InitializeMessageTemplates();

                // تفعيل الإعدادات الاحترافية
                InitializeProfessionalSettings();

                System.Diagnostics.Debug.WriteLine("✅ تم تفعيل النماذج الاحترافية للرسائل بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تفعيل النماذج الاحترافية: {ex.Message}");
                // العودة للنماذج الأساسية في حالة الخطأ
                InitializeMessageTemplates();
            }
        }

        /// <summary>
        /// تفعيل الإعدادات الاحترافية
        /// </summary>
        private void InitializeProfessionalSettings()
        {
            try
            {
                // تفعيل الإعدادات المتقدمة للرسائل
                IsWhatsAppSelected = true; // تفعيل WhatsApp كافتراضي
                IsIndividualSending = true; // تفعيل الإرسال الفردي كافتراضي



                System.Diagnostics.Debug.WriteLine("✅ تم تفعيل الإعدادات الاحترافية");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تفعيل الإعدادات الاحترافية: {ex.Message}");
            }
        }

        private void InitializeMessageTemplates()
        {
            MessageTemplates.Clear();
            MessageTemplates.Add(new MessageTemplate
            {
                Name = "قالب الزيارة الميدانية",
                Template = @"/الأخ{DriverName}،
يرجى تقديم عرض سعركم خلال 24 ساعة وذلك للسفر لمدة ({daysToShow} يوم) مع الأخ:ت/ {visitorsText}
في المناطق التالية:
{Itinerary} 📅 تاريخ النزول: {StartDate}...وشكراً - "
            });

            MessageTemplates.Add(new MessageTemplate
            {
                Name = "قالب مختصر",
                Template = @"الأخ/{DriverName} المحترم،
مطلوب عرض سعر للسفر ({DaysCount} يوم) مع {Visitors}
التاريخ: {StartDate} - {EndDate}
وشكراً"
            });

            MessageTemplates.Add(new MessageTemplate
            {
                Name = "قالب تذكير",
                Template = @"الأخ/{DriverName} المحترم،
تذكير بطلب عرض السعر للزيارة الميدانية
المدة: {DaysCount} يوم
يرجى الرد في أقرب وقت
شكراً"
            });
        }

        private async Task InitializeAsync()
        {
            try
            {
                await RefreshDataAsync();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// إجبار إعادة تحميل البيانات (للاستخدام عند الحاجة لتحديث قسري)
        /// </summary>
        public async Task ForceRefreshDataAsync()
        {
            _isDataLoaded = false; // إجبار إعادة التحميل
            await RefreshDataAsync();
        }

        private async Task RefreshDataAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 RefreshDataAsync: بدء تحميل بيانات السائقين...");

                // التحقق من أن البيانات لم تُحمل مسبقاً لتجنب التكرار
                if (_isDataLoaded && AllDrivers.Count > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"✅ البيانات محملة مسبقاً - {AllDrivers.Count} سائق، تخطي التحميل");
                    return;
                }

                // تنظيف شامل للبيانات القديمة وإزالة Event Handlers
                System.Diagnostics.Debug.WriteLine($"🧹 تنظيف شامل للبيانات القديمة...");
                CleanupAllData();

                var drivers = await _dataService.GetDriversAsync();
                System.Diagnostics.Debug.WriteLine($"📥 تحميل {drivers.Count} سائق جديد");

                foreach (var driver in drivers)
                {
                    var driverModel = new DriverModel
                    {
                        DriverCode = driver.DriverCode,
                        Name = driver.Name,
                        PhoneNumber = driver.PhoneNumber,
                        VehicleType = driver.VehicleType,
                        VehicleCapacity = driver.VehicleCapacity,
                        VehicleNumber = driver.VehicleNumber,
                        IsSelected = false
                    };

                    // إضافة Event Handler مع تتبع للإزالة لاحقاً
                    PropertyChangedEventHandler handler = (s, e) =>
                    {
                        if (e.PropertyName == nameof(DriverModel.IsSelected))
                        {
                            UpdateSelectedCount();
                            OnPropertyChanged(nameof(SelectedDriversDisplayText));
                        }
                    };

                    driverModel.PropertyChanged += handler;
                    AllDrivers.Add(driverModel);
                }

                FilterDrivers();

                // تحديث وقت آخر تحديث
                LastUpdateTime = DateTime.Now.ToString("yyyy/MM/dd HH:mm");

                // تحديث الإحصائيات
                OnPropertyChanged(nameof(TotalDriversCount));
                OnPropertyChanged(nameof(AvailableDriversCount));
                OnPropertyChanged(nameof(SelectedDriversCount));
                OnPropertyChanged(nameof(SelectedDriversDisplayText));
                OnPropertyChanged(nameof(StatisticsDebugText));

                // تعيين حالة تحميل البيانات
                _isDataLoaded = true;

                System.Diagnostics.Debug.WriteLine($"✅ RefreshDataAsync: تم تحميل {AllDrivers.Count} سائق بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ RefreshDataAsync Error: {ex.Message}");
                MessageBox.Show($"خطأ في تحميل بيانات السائقين: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        public void FilterDrivers()
        {
            try
            {
                if (AllDrivers == null)
                {
                    FilteredDrivers.Clear();
                    return;
                }

                var filtered = AllDrivers.AsEnumerable();

                // Filter by search text
                if (!string.IsNullOrWhiteSpace(SearchText))
                {
                    filtered = filtered.Where(d =>
                        d.Name?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) == true ||
                        d.DriverCode?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) == true ||
                        d.PhoneNumber?.Contains(SearchText, StringComparison.OrdinalIgnoreCase) == true);
                }

                // Filter by vehicle type
                if (SelectedVehicleTypes?.Count > 0)
                {
                    filtered = filtered.Where(d =>
                        !string.IsNullOrEmpty(d.VehicleType) &&
                        SelectedVehicleTypes.Any(vt => d.VehicleType.Contains(vt, StringComparison.OrdinalIgnoreCase)));
                }

                // Filter by vehicle capacity
                if (SelectedVehicleCapacities?.Count > 0)
                {
                    filtered = filtered.Where(d =>
                        !string.IsNullOrEmpty(d.VehicleCapacity) &&
                        SelectedVehicleCapacities.Any(vc => d.VehicleCapacity.Contains(vc, StringComparison.OrdinalIgnoreCase)));
                }

                // Clear and repopulate FilteredDrivers
                FilteredDrivers.Clear();
                foreach (var driver in filtered)
                {
                    FilteredDrivers.Add(driver);
                }

                UpdateSelectedCount();

                // تحديث الإحصائيات بعد الفلترة
                OnPropertyChanged(nameof(TotalDriversCount));
                OnPropertyChanged(nameof(AvailableDriversCount));
                OnPropertyChanged(nameof(StatisticsDebugText));

                System.Diagnostics.Debug.WriteLine($"🔍 Filtered drivers: {FilteredDrivers.Count} out of {AllDrivers.Count}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error filtering drivers: {ex.Message}");
                FilteredDrivers.Clear();
            }
        }

        private void UpdateSelectedCount()
        {
            Console.WriteLine($"🔄 UpdateSelectedCount called");
            // تحديث قائمة السائقين المحددين
            SelectedDrivers.Clear();
            foreach (var driver in AllDrivers.Where(d => d.IsSelected))
            {
                SelectedDrivers.Add(driver);
            }

            // تحديث نوع الإرسال بناءً على عدد السائقين المحددين
            var selectedCount = SelectedDrivers.Count;
            Console.WriteLine($"🔄 UpdateSelectedCount: selectedCount = {selectedCount}");
            if (selectedCount > 1)
            {
                // إذا كان هناك أكثر من سائق، فعل الإرسال الجماعي
                IsGroupSending = true;
                IsIndividualSending = false;

                // توليد رسالة جماعية
                GenerateGroupMessage();

                System.Diagnostics.Debug.WriteLine($"🔄 Switched to Group Sending - {selectedCount} drivers selected");
            }
            else if (selectedCount == 1)
            {
                // إذا كان هناك سائق واحد، فعل الإرسال الفردي
                IsIndividualSending = true;
                IsGroupSending = false;
                SelectedDriver = SelectedDrivers.FirstOrDefault();

                // توليد رسالة فردية (سيتم توليدها تلقائياً عند تحديد SelectedDriver)

                System.Diagnostics.Debug.WriteLine($"🔄 Switched to Individual Sending - 1 driver selected");
            }
            else
            {
                // إذا لم يكن هناك سائقين محددين، امسح الرسالة
                MessageContent = "";
                System.Diagnostics.Debug.WriteLine($"🔄 No drivers selected - cleared message");
            }

            // تحديث جميع الإحصائيات
            OnPropertyChanged(nameof(TotalDriversCount));
            OnPropertyChanged(nameof(AvailableDriversCount));
            OnPropertyChanged(nameof(SelectedDriversCount));
            OnPropertyChanged(nameof(SelectedDriversDisplayText));
            OnPropertyChanged(nameof(HasSelectedDrivers));
            OnPropertyChanged(nameof(StatisticsDebugText));
        }

        private void SelectAllDrivers()
        {
            foreach (var driver in FilteredDrivers)
            {
                driver.IsSelected = true;
            }
            UpdateSelectedCount();
        }

        private void ClearDriverSelection()
        {
            foreach (var driver in FilteredDrivers)
            {
                driver.IsSelected = false;
            }
            UpdateSelectedCount();
        }

        private void SelectAllForSending()
        {
            foreach (var driver in SelectedDrivers)
            {
                driver.IsSelectedForSending = true;
            }
        }

        private void ClearSendingSelection()
        {
            foreach (var driver in SelectedDrivers)
            {
                driver.IsSelectedForSending = false;
            }
        }

        /// <summary>
        /// توليد رسالة جماعية للسائقين المحددين (باستخدام اسم أول سائق كمثال)
        /// </summary>
        private void GenerateGroupMessage()
        {
            try
            {
                Console.WriteLine($"🔍 GROUP MESSAGE - SelectedVisit: {SelectedVisit?.VisitNumber ?? "NULL"}");
                Console.WriteLine($"🔍 GROUP MESSAGE - SelectedDrivers count: {SelectedDrivers?.Count ?? 0}");

                if (SelectedVisit == null || SelectedDrivers?.Count == 0)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ GROUP MESSAGE - Missing data: SelectedVisit={SelectedVisit?.VisitNumber ?? "NULL"}, Drivers={SelectedDrivers?.Count ?? 0}");
                    MessageContent = "";
                    return;
                }

                // استخدام اسم أول سائق محدد كمثال في القالب
                var firstDriverName = SelectedDrivers.FirstOrDefault()?.Name ?? "السائق";

                // بناء نص خط السير مع تفاصيل الأيام (نفس منطق GenerateMessageForDriver)
                var itineraryText = "";
                Console.WriteLine($"🔍 GROUP MESSAGE - SelectedVisit.Itinerary count: {SelectedVisit.Itinerary?.Count ?? 0}");
                if (SelectedVisit.Itinerary?.Any() == true)
                {
                    // استخدام نفس منطق GenerateMessageForDriver مع أسماء الأيام العربية
                    for (int i = 0; i < SelectedVisit.Itinerary.Count; i++)
                    {
                        Console.WriteLine($"🔍 GROUP MESSAGE - Processing day {i + 1}: '{SelectedVisit.Itinerary[i]}'");
                        if (!string.IsNullOrWhiteSpace(SelectedVisit.Itinerary[i]))
                        {
                            var dayName = GetArabicDayName(i + 1);
                            var formattedDay = $"- اليوم {dayName}: {SelectedVisit.Itinerary[i]}";
                            itineraryText += formattedDay + "\n";
                            Console.WriteLine($"✅ GROUP MESSAGE - Added formatted day: '{formattedDay}'");
                        }
                    }

                    Console.WriteLine($"✅ GROUP MESSAGE - Final itinerary text: '{itineraryText}'");
                }
                else
                {
                    itineraryText = "- لم يتم تحديد خط السير\n";
                    Console.WriteLine($"❌ GROUP MESSAGE - No itinerary data found, using default text");
                }

                // بناء نص القائمين بالزيارة (الأسماء فقط بدون الرتب)
                var visitorsText = "الفريق المختص";
                if (SelectedVisit.Visitors?.Any() == true)
                {
                    visitorsText = string.Join(" و ", SelectedVisit.Visitors.Select(v => v.OfficerName));
                }

                // حساب تواريخ النزول والعودة
                var startDate = SelectedVisit.DepartureDate.ToString("dd/MM/yyyy");
                var endDate = SelectedVisit.ReturnDate.ToString("dd/MM/yyyy");

                // حساب عدد الأيام بطريقة ذكية
                var daysToShow = SelectedVisit.TotalProjectDays > 0 ? SelectedVisit.TotalProjectDays : SelectedVisit.DaysCount;

                // إذا كانت الأيام صفر، احسب من التواريخ
                if (daysToShow <= 0)
                {
                    var daysDifference = (SelectedVisit.ReturnDate - SelectedVisit.DepartureDate).Days + 1;
                    daysToShow = daysDifference > 0 ? daysDifference : 3; // افتراضي 3 أيام
                }

                System.Diagnostics.Debug.WriteLine($"📊 Days calculation: TotalProjectDays={SelectedVisit.TotalProjectDays}, DaysCount={SelectedVisit.DaysCount}, Final={daysToShow}");

                // رسالة شخصية باسم أول سائق (سيتم تخصيصها لكل سائق عند الإرسال الفعلي)
                MessageContent = $@"الأخ/{firstDriverName} ،
يرجى تقديم عرض سعركم خلال 24 ساعة وذلك للسفر لمدة ({daysToShow} أيام) مع الأخ:ت/ {visitorsText}
في المناطق التالية:
{itineraryText} 📅 تاريخ النزول: {startDate} ...وشكراً";

                System.Diagnostics.Debug.WriteLine($"✅ Generated personalized group message template using first driver: {firstDriverName} for {SelectedDrivers.Count} drivers");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error generating group message: {ex.Message}");
                MessageContent = "الأخ/السائق المحترم، يرجى تقديم عرض سعركم للزيارة الميدانية";
            }
        }

        private void GenerateMessageForDriver(DriverModel driverModel)
        {
            if (SelectedVisit == null)
            {
                // Don't generate message without visit data
                MessageContent = "";
                return;
            }

            try
            {
                var visitorsText = "غير محدد";
                if (SelectedVisit.Visitors != null && SelectedVisit.Visitors.Any())
                {
                    var visitorNames = SelectedVisit.Visitors
                        .Where(v => !string.IsNullOrWhiteSpace(v.OfficerName))
                        .Select(v => v.OfficerName)
                        .ToList();

                    if (visitorNames.Any())
                    {
                        visitorsText = string.Join("، ", visitorNames);
                    }
                }

                var itineraryText = "";
                if (SelectedVisit.Itinerary != null && SelectedVisit.Itinerary.Any())
                {
                    for (int i = 0; i < SelectedVisit.Itinerary.Count; i++)
                    {
                        if (!string.IsNullOrWhiteSpace(SelectedVisit.Itinerary[i]))
                        {
                            var dayName = GetArabicDayName(i + 1);
                            itineraryText += $"- اليوم {dayName}: {SelectedVisit.Itinerary[i]}\n";
                        }
                    }
                }
                else
                {
                    itineraryText = "- لم يتم تحديد خط السير\n";
                }

                // حساب تواريخ النزول والعودة
                var startDate = SelectedVisit.DepartureDate.ToString("dd/MM/yyyy");
                var endDate = SelectedVisit.ReturnDate.ToString("dd/MM/yyyy");

                var daysToShow = SelectedVisit.TotalProjectDays > 0 ? SelectedVisit.TotalProjectDays : SelectedVisit.DaysCount;
                MessageContent = $@"الأخ/{driverModel.Name} ،
يرجى تقديم عرض سعركم خلال 24 ساعة وذلك للسفر لمدة ({daysToShow} أيام) مع الأخ:ت/ {visitorsText}
في المناطق التالية:
{itineraryText} 📅 تاريخ النزول: {startDate} ...وشكراً";

                System.Diagnostics.Debug.WriteLine($"✅ Generated message for driver: {driverModel.Name}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error generating message: {ex.Message}");
                MessageBox.Show($"خطأ في توليد الرسالة: {ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RefreshMessageContent()
        {
            if (SelectedDriver != null)
            {
                GenerateMessageForDriver(SelectedDriver);
            }
            else
            {
                GenerateDefaultMessage();
            }
        }

        private void GenerateDefaultMessage()
        {
            // Don't generate default message - only when driver is selected
            MessageContent = "";
        }

        private async void GenerateMessageFromSelectedTemplate()
        {
            try
            {
                // تحميل العروض المحفوظة إذا لم تكن موجودة
                if (OffersResults?.Count == 0 && SelectedVisit != null)
                {
                    System.Diagnostics.Debug.WriteLine($"🔄 لا توجد عروض معروضة، محاولة تحميل العروض المحفوظة للزيارة: {VisitNumber}");
                    await LoadSavedOffersFromDatabase();
                }

                if (SelectedMessageTemplate != null)
                {
                    if (SelectedDriver != null)
                    {
                        GenerateMessageFromTemplate(SelectedMessageTemplate);
                    }
                    else
                    {
                        MessageContent = SelectedMessageTemplate.Template;
                    }
                    System.Diagnostics.Debug.WriteLine($"✅ Generated message from template: {SelectedMessageTemplate.Name}");
                }
                else
                {
                    MessageBox.Show("يرجى اختيار قالب رسالة أولاً", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في توليد الرسالة: {ex.Message}");
            }
        }

        private void GenerateMessageFromTemplate(MessageTemplate template)
        {
            if (SelectedVisit == null || SelectedDriver == null || template == null)
            {
                MessageContent = template?.Template ?? "";
                return;
            }

            try
            {
                var visitorsText = "غير محدد";
                if (SelectedVisit.Visitors != null && SelectedVisit.Visitors.Any())
                {
                    var visitorNames = SelectedVisit.Visitors
                        .Where(v => !string.IsNullOrWhiteSpace(v.OfficerName))
                        .Select(v => v.OfficerName)
                        .ToList();

                    if (visitorNames.Any())
                    {
                        visitorsText = string.Join("، ", visitorNames);
                    }
                }

                var itineraryText = "";
                if (SelectedVisit.Itinerary != null && SelectedVisit.Itinerary.Any())
                {
                    for (int i = 0; i < SelectedVisit.Itinerary.Count; i++)
                    {
                        if (!string.IsNullOrWhiteSpace(SelectedVisit.Itinerary[i]))
                        {
                            var dayName = GetArabicDayName(i + 1);
                            itineraryText += $"- اليوم {dayName}: {SelectedVisit.Itinerary[i]}\n";
                        }
                    }
                }
                else
                {
                    itineraryText = "- لم يتم تحديد خط السير\n";
                }

                var startDate = SelectedVisit.DepartureDate.ToString("dd/MM/yyyy");
                var endDate = SelectedVisit.ReturnDate.ToString("dd/MM/yyyy");

                var daysToShow = SelectedVisit.TotalProjectDays > 0 ? SelectedVisit.TotalProjectDays : SelectedVisit.DaysCount;
                MessageContent = template.Template
                    .Replace("{DriverName}", SelectedDriver.Name)
                    .Replace("{DaysCount}", daysToShow.ToString())
                    .Replace("{Visitors}", visitorsText)
                    .Replace("{Itinerary}", itineraryText)
                    .Replace("{StartDate}", startDate)
                    .Replace("{EndDate}", endDate);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error generating message from template: {ex.Message}");
                MessageContent = template.Template;
            }
        }

        private string GetArabicDayName(int dayNumber)
        {
            return dayNumber switch
            {
                1 => "الأول",
                2 => "الثاني",
                3 => "الثالث",
                4 => "الرابع",
                5 => "الخامس",
                6 => "السادس",
                7 => "السابع",
                8 => "الثامن",
                9 => "التاسع",
                10 => "العاشر",
                _ => dayNumber.ToString()
            };
        }

        private async Task SendIndividualMessageAsync()
        {
            if (SelectedDriver == null)
            {
                MessageBox.Show("يرجى اختيار سائق أولاً", "تنبيه",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(MessageContent))
            {
                MessageBox.Show("يرجى كتابة محتوى الرسالة أولاً", "تنبيه",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(SelectedDriver.PhoneNumber))
            {
                MessageBox.Show($"رقم هاتف السائق {SelectedDriver.Name} غير متوفر", "تنبيه",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"هل تريد إرسال الرسالة للسائق {SelectedDriver.Name}؟",
                                       "تأكيد الإرسال", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    // تحديد طريقة الإرسال
                    if (IsWhatsAppSelected)
                    {
                        System.Diagnostics.Debug.WriteLine($"📱 Sending WhatsApp message to {SelectedDriver.Name} ({SelectedDriver.PhoneNumber})");

                        // إرسال الرسالة بدون عرض رسائل مضللة
                        bool success = await _whatsAppService.SendMessageAsync(SelectedDriver.PhoneNumber, MessageContent);

                        // لا نعرض أي رسالة - النظام سيحاول الإرسال فقط
                        System.Diagnostics.Debug.WriteLine($"📱 WhatsApp send attempt completed. Success: {success}");
                    }
                    else if (IsSMSSelected)
                    {
                        // TODO: تطبيق إرسال SMS
                        MessageBox.Show($"📱 إرسال SMS للسائق {SelectedDriver.Name}\n\nهذه الوظيفة قيد التطوير",
                                      "SMS", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else if (IsEmailSelected)
                    {
                        // TODO: تطبيق إرسال Email
                        MessageBox.Show($"📧 إرسال Email للسائق {SelectedDriver.Name}\n\nهذه الوظيفة قيد التطوير",
                                      "Email", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("يرجى اختيار طريقة الإرسال (WhatsApp, SMS, أو Email)", "تنبيه",
                                      MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ Error sending message: {ex.Message}");
                    MessageBox.Show($"حدث خطأ أثناء إرسال الرسالة:\n{ex.Message}", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private async Task SendGroupMessageAsync()
        {
            // استخدام السائقين المختارين للإرسال فقط
            var selectedDrivers = AllDrivers.Where(d => d.IsSelected && d.IsSelectedForSending).ToList();

            if (!selectedDrivers.Any())
            {
                MessageBox.Show("يرجى اختيار سائق واحد على الأقل للإرسال من القائمة أدناه", "تنبيه",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(MessageContent))
            {
                MessageBox.Show("يرجى كتابة محتوى الرسالة أولاً", "تنبيه",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // التحقق من أرقام الهواتف
            var driversWithoutPhone = selectedDrivers.Where(d => string.IsNullOrWhiteSpace(d.PhoneNumber)).ToList();
            if (driversWithoutPhone.Any())
            {
                var names = string.Join(", ", driversWithoutPhone.Select(d => d.Name));
                MessageBox.Show($"السائقين التاليين لا يملكون أرقام هواتف:\n{names}", "تنبيه",
                              MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"هل تريد إرسال الرسالة لـ {selectedDrivers.Count} سائق؟",
                                       "تأكيد الإرسال الجماعي", MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    if (IsWhatsAppSelected)
                    {
                        System.Diagnostics.Debug.WriteLine($"📱 Sending WhatsApp messages to {selectedDrivers.Count} drivers");

                        // إرسال الرسائل واحدة تلو الأخرى مع تخصيص كل رسالة باسم السائق
                        int successCount = 0;
                        foreach (var driver in selectedDrivers)
                        {
                            try
                            {
                                // تخصيص الرسالة لكل سائق باسمه الشخصي
                                string personalizedMessage = GenerateDriverMessage(driver.Name);

                                System.Diagnostics.Debug.WriteLine($"📝 Personalized message for {driver.Name}: {personalizedMessage.Substring(0, Math.Min(50, personalizedMessage.Length))}...");

                                bool success = await _whatsAppService.SendMessageAsync(driver.PhoneNumber, personalizedMessage);
                                if (success)
                                {
                                    successCount++;
                                    System.Diagnostics.Debug.WriteLine($"✅ Personalized message sent to {driver.Name}");

                                    // انتظار بين الرسائل لتجنب الإرهاق
                                    if (selectedDrivers.Count > 1)
                                        await Task.Delay(3000);
                                }
                                else
                                {
                                    System.Diagnostics.Debug.WriteLine($"❌ Failed to send message to {driver.Name}");
                                }
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"❌ Error sending to {driver.Name}: {ex.Message}");
                            }
                        }

                        if (successCount > 0)
                        {
                            _whatsAppService.ShowSuccessMessage("", true, successCount);
                        }
                        else
                        {
                            _whatsAppService.ShowErrorMessage("فشل في إرسال جميع الرسائل");
                        }
                    }
                    else if (IsSMSSelected)
                    {
                        // TODO: تطبيق إرسال SMS جماعي
                        MessageBox.Show($"📱 إرسال SMS جماعي لـ {selectedDrivers.Count} سائق\n\nهذه الوظيفة قيد التطوير",
                                      "SMS", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else if (IsEmailSelected)
                    {
                        // TODO: تطبيق إرسال Email جماعي
                        MessageBox.Show($"📧 إرسال Email جماعي لـ {selectedDrivers.Count} سائق\n\nهذه الوظيفة قيد التطوير",
                                      "Email", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("يرجى اختيار طريقة الإرسال (WhatsApp, SMS, أو Email)", "تنبيه",
                                      MessageBoxButton.OK, MessageBoxImage.Warning);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ Error in group messaging: {ex.Message}");
                    MessageBox.Show($"حدث خطأ أثناء الإرسال الجماعي:\n{ex.Message}", "خطأ",
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// إرسال الرسالة (موحد)
        /// </summary>
        private async Task SendMessageAsync()
        {
            try
            {
                // التحقق من وجود سائقين محددين أولاً
                var selectedDriversCount = AllDrivers.Count(d => d.IsSelected);

                System.Diagnostics.Debug.WriteLine($"🔍 SendMessageAsync - Selected drivers: {selectedDriversCount}");
                System.Diagnostics.Debug.WriteLine($"🔍 SendMessageAsync - IsIndividualSending: {IsIndividualSending}");
                System.Diagnostics.Debug.WriteLine($"🔍 SendMessageAsync - IsGroupSending: {IsGroupSending}");
                System.Diagnostics.Debug.WriteLine($"🔍 SendMessageAsync - MessageContent: '{MessageContent?.Substring(0, Math.Min(50, MessageContent?.Length ?? 0))}...'");

                if (selectedDriversCount == 0)
                {
                    MessageBox.Show("يرجى اختيار سائق واحد على الأقل أولاً", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (string.IsNullOrWhiteSpace(MessageContent))
                {
                    MessageBox.Show("يرجى كتابة محتوى الرسالة أولاً", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                if (IsIndividualSending)
                {
                    await SendIndividualMessageAsync();
                }
                else if (IsGroupSending)
                {
                    await SendGroupMessageAsync();
                }
                else
                {
                    MessageBox.Show("يرجى اختيار نوع الإرسال (فردي أو جماعي)", "تنبيه",
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error in unified send: {ex.Message}");
                MessageBox.Show($"حدث خطأ أثناء الإرسال:\n{ex.Message}", "خطأ",
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        #endregion

        #region INotifyPropertyChanged
        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// دالة عامة لتحديث خاصية معينة
        /// </summary>
        public void NotifyPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        /// <summary>
        /// تحديد نص حالة العرض بناءً على خصائص العرض
        /// </summary>
        public string GetOfferStatusText(DriverOffer offer)
        {
            if (!string.IsNullOrEmpty(offer.OfferStatus))
            {
                // إذا كانت الحالة محددة مسبقاً، استخدمها
                return offer.OfferStatus;
            }

            // تحديد الحالة بناءً على خصائص العرض
            if (offer.IsWinner)
                return "🏆 فائز";
            else if (offer.IsSelected)
                return "✅ معتمد";
            else
                return "تم التقديم";
        }

        #endregion
    }

    /// <summary>
    /// نموذج السائق للعرض
    /// </summary>
    public class DriverModel : INotifyPropertyChanged
    {
        private bool _isSelected;
        private bool _isSelectedForSending = true; // افتراضياً مختار للإرسال

        public string DriverCode { get; set; }
        public string Name { get; set; }
        public string PhoneNumber { get; set; }
        public string VehicleType { get; set; }
        public string VehicleCapacity { get; set; }
        public string VehicleNumber { get; set; }
        public string Status { get; set; } = "متاح";

        public bool IsSelected
        {
            get => _isSelected;
            set
            {
                if (_isSelected != value)
                {
                    _isSelected = value;
                    OnPropertyChanged();

                    // إشعار بتغيير الاختيار
                    SelectionChanged?.Invoke(this, EventArgs.Empty);
                }
            }
        }

        public bool IsSelectedForSending
        {
            get => _isSelectedForSending;
            set
            {
                if (_isSelectedForSending != value)
                {
                    _isSelectedForSending = value;
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        public event EventHandler SelectionChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }

        #region IDisposable Implementation

        private bool _disposed = false;

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    // تنظيف شامل عند التخلص من الكائن
                    System.Diagnostics.Debug.WriteLine("🗑️ تم التخلص من ProfessionalMessagesViewModel");
                }
                _disposed = true;
            }
        }

        #endregion
    }

    /// <summary>
    /// RelayCommand للأوامر
    /// </summary>
    public class RelayCommand : ICommand
    {
        private readonly Action _execute;
        private readonly Func<bool> _canExecute;

        public RelayCommand(Action execute, Func<bool> canExecute = null)
        {
            _execute = execute ?? throw new ArgumentNullException(nameof(execute));
            _canExecute = canExecute;
        }

        public event EventHandler CanExecuteChanged
        {
            add { CommandManager.RequerySuggested += value; }
            remove { CommandManager.RequerySuggested -= value; }
        }

        public bool CanExecute(object parameter) => _canExecute?.Invoke() ?? true;

        public void Execute(object parameter) => _execute();
    }

    /// <summary>
    /// قالب الرسالة
    /// </summary>
    public class MessageTemplate
    {
        public string Name { get; set; } = string.Empty;
        public string Template { get; set; } = string.Empty;
    }

    /// <summary>
    /// نموذج نتيجة عرض السعر
    /// </summary>
    public class OfferResult : INotifyPropertyChanged
    {
        private string _driverName = string.Empty;
        private string _offeredPrice = string.Empty;
        private string _status = string.Empty;
        private string _submissionDate = string.Empty;
        private string _messageText = string.Empty;

        public string DriverName
        {
            get => _driverName;
            set
            {
                if (_driverName != value)
                {
                    _driverName = value;
                    OnPropertyChanged();
                }
            }
        }

        public string OfferedPrice
        {
            get => _offeredPrice;
            set
            {
                if (_offeredPrice != value)
                {
                    _offeredPrice = value;
                    OnPropertyChanged();
                }
            }
        }

        public string Status
        {
            get => _status;
            set
            {
                if (_status != value)
                {
                    _status = value;
                    OnPropertyChanged();
                }
            }
        }

        public string SubmissionDate
        {
            get => _submissionDate;
            set
            {
                if (_submissionDate != value)
                {
                    _submissionDate = value;
                    OnPropertyChanged();
                }
            }
        }

        public string MessageText
        {
            get => _messageText;
            set
            {
                if (_messageText != value)
                {
                    _messageText = value;
                    OnPropertyChanged();
                }
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;

        protected virtual void OnPropertyChanged([CallerMemberName] string propertyName = null)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }


    }
}
